import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { ZodError } from 'zod';
import { logger } from '../utils/logger';
import { createErrorResponse } from '@claude-code-manage/shared';

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // 记录错误日志
  logger.error('API错误:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
  });

  // Prisma数据库错误
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        // 唯一约束违反
        const target = error.meta?.target as string[];
        const field = target?.[0] || '字段';
        return res.status(409).json(
          createErrorResponse(
            '数据冲突',
            `${field}已存在，请使用其他值`
          )
        );
      
      case 'P2025':
        // 记录不存在
        return res.status(404).json(
          createErrorResponse('资源不存在', '请求的资源未找到')
        );
      
      case 'P2003':
        // 外键约束违反
        return res.status(400).json(
          createErrorResponse('关联数据错误', '相关数据不存在或已被删除')
        );
      
      default:
        return res.status(500).json(
          createErrorResponse('数据库错误', '数据操作失败，请稍后重试')
        );
    }
  }

  // Prisma客户端错误
  if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    return res.status(500).json(
      createErrorResponse('数据库连接错误', '数据库服务暂时不可用')
    );
  }

  // Zod验证错误
  if (error instanceof ZodError) {
    const errorMessages = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
    }));

    return res.status(400).json(
      createErrorResponse(
        '请求参数验证失败',
        errorMessages.map(e => `${e.field}: ${e.message}`).join(', ')
      )
    );
  }

  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json(
      createErrorResponse('认证失败', '无效的访问令牌')
    );
  }

  if (error.name === 'TokenExpiredError') {
    return res.status(401).json(
      createErrorResponse('认证过期', '访问令牌已过期，请重新登录')
    );
  }

  // 自定义业务错误
  if (error.name === 'BusinessError') {
    return res.status(400).json(
      createErrorResponse('业务错误', error.message)
    );
  }

  // 权限错误
  if (error.name === 'ForbiddenError') {
    return res.status(403).json(
      createErrorResponse('权限不足', error.message)
    );
  }

  // 未找到错误
  if (error.name === 'NotFoundError') {
    return res.status(404).json(
      createErrorResponse('资源不存在', error.message)
    );
  }

  // 默认服务器错误
  return res.status(500).json(
    createErrorResponse(
      '服务器内部错误',
      process.env.NODE_ENV === 'development' ? error.message : '服务暂时不可用，请稍后重试'
    )
  );
};

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (req: Request, res: Response) => {
  logger.warn('404错误:', {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json(
    createErrorResponse(
      '接口不存在',
      `未找到请求的接口: ${req.method} ${req.url}`
    )
  );
};

/**
 * 自定义错误类
 */
export class BusinessError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BusinessError';
  }
}

export class ForbiddenError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}
