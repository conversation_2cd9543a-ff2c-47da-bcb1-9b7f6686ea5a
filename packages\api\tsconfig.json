{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": false, "declarationMap": false, "sourceMap": true, "noEmit": false, "target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts"], "ts-node": {"esm": false}}