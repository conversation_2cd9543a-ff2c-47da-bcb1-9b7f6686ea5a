import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { logger } from '../utils/logger';
import { 
  LoginRequest, 
  RegisterRequest, 
  TokenPayload,
  createSuccessResponse,
  createErrorResponse,
} from '@claude-code-manage/shared';
import { BusinessError, NotFoundError } from '../middleware/errorHandler';

export class AuthService {
  /**
   * 用户登录
   */
  async login(loginData: LoginRequest) {
    const { email, password } = loginData;

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 检查用户是否被禁用
    if (!user.isActive) {
      throw new BusinessError('用户账户已被禁用，请联系管理员');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new BusinessError('邮箱或密码错误');
    }

    // 生成JWT token
    const tokenPayload: Omit<TokenPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    const token = jwt.sign(tokenPayload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });

    // 计算过期时间
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7天后过期

    // 保存会话到数据库
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt,
      },
    });

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    logger.info('用户登录成功:', { userId: user.id, email: user.email });

    return createSuccessResponse({
      token,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        dailyLimit: user.dailyLimit,
        monthlyLimit: user.monthlyLimit,
        dailyUsed: user.dailyUsed,
        monthlyUsed: user.monthlyUsed,
      },
    }, '登录成功');
  }

  /**
   * 用户注册
   */
  async register(registerData: RegisterRequest) {
    const { email, username, password } = registerData;

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      throw new BusinessError('邮箱已被注册');
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUserByUsername) {
      throw new BusinessError('用户名已被使用');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        dailyLimit: config.limits.defaultDaily,
        monthlyLimit: config.limits.defaultMonthly,
      },
    });

    logger.info('用户注册成功:', { userId: user.id, email: user.email });

    return createSuccessResponse({
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      },
    }, '注册成功');
  }

  /**
   * 用户登出
   */
  async logout(token: string) {
    // 删除会话
    await prisma.session.deleteMany({
      where: { token },
    });

    logger.info('用户登出成功');

    return createSuccessResponse(null, '登出成功');
  }

  /**
   * 获取用户信息
   */
  async getProfile(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        dailyLimit: true,
        monthlyLimit: true,
        dailyUsed: true,
        monthlyUsed: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    return createSuccessResponse(user);
  }

  /**
   * 更新用户信息
   */
  async updateProfile(userId: string, updateData: { username?: string }) {
    const { username } = updateData;

    // 如果要更新用户名，检查是否已存在
    if (username) {
      const existingUser = await prisma.user.findFirst({
        where: {
          username,
          id: { not: userId },
        },
      });

      if (existingUser) {
        throw new BusinessError('用户名已被使用');
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { username },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        dailyLimit: true,
        monthlyLimit: true,
        dailyUsed: true,
        monthlyUsed: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    logger.info('用户信息更新成功:', { userId, updateData });

    return createSuccessResponse(updatedUser, '用户信息更新成功');
  }

  /**
   * 验证token
   */
  async validateToken(token: string) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret) as TokenPayload;
      
      const session = await prisma.session.findUnique({
        where: { token },
        include: { user: true },
      });

      if (!session || session.expiresAt < new Date()) {
        throw new BusinessError('令牌已过期');
      }

      if (!session.user.isActive) {
        throw new BusinessError('用户账户已被禁用');
      }

      return createSuccessResponse({
        valid: true,
        user: {
          id: session.user.id,
          email: session.user.email,
          username: session.user.username,
          role: session.user.role,
        },
      });
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new BusinessError('无效的令牌');
      }
      throw error;
    }
  }
}
