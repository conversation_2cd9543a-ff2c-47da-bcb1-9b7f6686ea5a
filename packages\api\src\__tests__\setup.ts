import { PrismaClient } from '@prisma/client';

// 测试数据库设置
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/claude_code_test',
    },
  },
});

// 全局测试设置
beforeAll(async () => {
  // 连接测试数据库
  await prisma.$connect();
});

// 每个测试后清理数据
afterEach(async () => {
  // 清理测试数据
  await prisma.usageRecord.deleteMany();
  await prisma.session.deleteMany();
  await prisma.card.deleteMany();
  await prisma.user.deleteMany();
  await prisma.systemConfig.deleteMany();
  await prisma.auditLog.deleteMany();
});

// 全局测试清理
afterAll(async () => {
  // 断开数据库连接
  await prisma.$disconnect();
});

export { prisma };
