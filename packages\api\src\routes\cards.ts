import { Router } from 'express';
import { CardController } from '../controllers/cardController';
import { validate } from '../middleware/validation';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import { redeemRateLimit } from '../middleware/rateLimiter';
import { CreateCardSchema, RedeemCardSchema } from '@claude-code-manage/shared';

const router = Router();
const cardController = new CardController();

// 兑换卡密 (用户)
router.post('/redeem',
  authenticateToken,
  redeemRateLimit,
  validate(RedeemCardSchema),
  cardController.redeemCard
);

// 创建卡密 (管理员)
router.post('/',
  authenticateToken,
  requireAdmin,
  validate(CreateCardSchema),
  cardController.createCard
);

// 批量创建卡密 (管理员)
router.post('/batch',
  authenticateToken,
  requireAdmin,
  cardController.createCards
);

// 获取卡密列表 (管理员)
router.get('/',
  authenticateToken,
  requireAdmin,
  cardController.getCards
);

// 删除卡密 (管理员)
router.delete('/:id',
  authenticateToken,
  requireAdmin,
  cardController.deleteCard
);

// 获取卡密统计 (管理员)
router.get('/stats',
  authenticateToken,
  requireAdmin,
  cardController.getCardStats
);

export default router;
