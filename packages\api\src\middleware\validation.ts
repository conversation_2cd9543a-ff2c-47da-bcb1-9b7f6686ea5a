import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { createErrorResponse } from '@claude-code-manage/shared';
import { logger } from '../utils/logger';

/**
 * 创建验证中间件
 */
export const validate = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // 验证请求体
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));

        logger.warn('请求验证失败:', { errors: errorMessages, body: req.body });

        return res.status(400).json(
          createErrorResponse(
            '请求参数验证失败',
            errorMessages.map(e => `${e.field}: ${e.message}`).join(', ')
          )
        );
      }

      logger.error('验证中间件错误:', error);
      return res.status(500).json(
        createErrorResponse('服务器内部错误', '请稍后重试')
      );
    }
  };
};

/**
 * 验证查询参数
 */
export const validateQuery = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.query);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));

        logger.warn('查询参数验证失败:', { errors: errorMessages, query: req.query });

        return res.status(400).json(
          createErrorResponse(
            '查询参数验证失败',
            errorMessages.map(e => `${e.field}: ${e.message}`).join(', ')
          )
        );
      }

      logger.error('查询参数验证中间件错误:', error);
      return res.status(500).json(
        createErrorResponse('服务器内部错误', '请稍后重试')
      );
    }
  };
};

/**
 * 验证路径参数
 */
export const validateParams = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.params);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));

        logger.warn('路径参数验证失败:', { errors: errorMessages, params: req.params });

        return res.status(400).json(
          createErrorResponse(
            '路径参数验证失败',
            errorMessages.map(e => `${e.field}: ${e.message}`).join(', ')
          )
        );
      }

      logger.error('路径参数验证中间件错误:', error);
      return res.status(500).json(
        createErrorResponse('服务器内部错误', '请稍后重试')
      );
    }
  };
};
