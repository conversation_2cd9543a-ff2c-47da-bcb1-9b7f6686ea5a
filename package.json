{"name": "claude-code-manage", "version": "1.0.0", "description": "Claude Code Management System - A shared Claude Max mirror with CLI, web interface, and admin panel", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:web\" \"npm run dev:admin\"", "dev:api": "cd packages/api && npm run dev", "dev:web": "cd packages/web && npm run dev", "dev:admin": "cd packages/admin && npm run dev", "dev:cli": "cd packages/cli && npm run dev", "build": "npm run build:api && npm run build:web && npm run build:admin && npm run build:cli", "build:api": "cd packages/api && npm run build", "build:web": "cd packages/web && npm run build", "build:admin": "cd packages/admin && npm run build", "build:cli": "cd packages/cli && npm run build", "test": "npm run test:api && npm run test:web && npm run test:admin && npm run test:cli", "test:api": "cd packages/api && npm test", "test:web": "cd packages/web && npm test", "test:admin": "cd packages/admin && npm test", "test:cli": "cd packages/cli && npm test", "lint": "eslint packages/*/src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint packages/*/src --ext .js,.jsx,.ts,.tsx --fix", "clean": "rimraf packages/*/dist packages/*/build packages/*/.next", "install:all": "npm install && npm run install:packages", "install:packages": "cd packages/api && npm install && cd ../web && npm install && cd ../admin && npm install && cd ../cli && npm install", "quick-test": "node scripts/quick-test.js", "setup": "npm run install:all && npm run build:shared", "build:shared": "cd packages/shared && npm run build"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.4.0", "chalk": "^4.1.2", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "supertest": "^6.3.0", "typescript": "^5.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/claude-code-manage.git"}, "keywords": ["claude", "ai", "cli", "proxy", "management", "mirror"], "author": "Your Name", "license": "MIT"}