import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { config } from '../config/env';
import { createErrorResponse } from '@claude-code-manage/shared';
import { logger } from '../utils/logger';

/**
 * 通用限流中间件
 */
export const generalRateLimit = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: createErrorResponse(
    '请求过于频繁',
    `每${config.rateLimit.windowMs / 1000 / 60}分钟最多${config.rateLimit.maxRequests}次请求`
  ),
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logger.warn('触发通用限流:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    });
    
    res.status(429).json(
      createErrorResponse(
        '请求过于频繁',
        `每${config.rateLimit.windowMs / 1000 / 60}分钟最多${config.rateLimit.maxRequests}次请求，请稍后再试`
      )
    );
  },
});

/**
 * 登录限流中间件
 */
export const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每15分钟最多5次登录尝试
  skipSuccessfulRequests: true,
  keyGenerator: (req: Request) => {
    // 基于IP和邮箱进行限流
    return `login_${req.ip}_${req.body.email || 'unknown'}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('触发登录限流:', {
      ip: req.ip,
      email: req.body.email,
      userAgent: req.get('User-Agent'),
    });
    
    res.status(429).json(
      createErrorResponse(
        '登录尝试过于频繁',
        '15分钟内最多允许5次登录尝试，请稍后再试'
      )
    );
  },
});

/**
 * 注册限流中间件
 */
export const registerRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每小时最多3次注册
  keyGenerator: (req: Request) => {
    return `register_${req.ip}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('触发注册限流:', {
      ip: req.ip,
      email: req.body.email,
      userAgent: req.get('User-Agent'),
    });
    
    res.status(429).json(
      createErrorResponse(
        '注册过于频繁',
        '每小时最多允许3次注册，请稍后再试'
      )
    );
  },
});

/**
 * Claude API限流中间件
 */
export const claudeRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 20, // 每分钟最多20次Claude请求
  keyGenerator: (req: Request) => {
    // 基于用户ID进行限流
    return `claude_${req.user?.id || req.ip}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('触发Claude API限流:', {
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
    
    res.status(429).json(
      createErrorResponse(
        'Claude请求过于频繁',
        '每分钟最多允许20次Claude请求，请稍后再试'
      )
    );
  },
});

/**
 * 卡密兑换限流中间件
 */
export const redeemRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 每小时最多10次兑换尝试
  keyGenerator: (req: Request) => {
    return `redeem_${req.user?.id || req.ip}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('触发卡密兑换限流:', {
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
    
    res.status(429).json(
      createErrorResponse(
        '卡密兑换过于频繁',
        '每小时最多允许10次卡密兑换尝试，请稍后再试'
      )
    );
  },
});
