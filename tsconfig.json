{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@shared/*": ["packages/shared/src/*"], "@api/*": ["packages/api/src/*"], "@web/*": ["packages/web/src/*"], "@admin/*": ["packages/admin/src/*"], "@cli/*": ["packages/cli/src/*"]}}, "include": ["packages/*/src/**/*", "packages/*/types/**/*"], "exclude": ["node_modules", "dist", "build", ".next"]}