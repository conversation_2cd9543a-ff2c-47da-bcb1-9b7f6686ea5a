import { Request, Response, NextFunction } from 'express';
import { CardService } from '../services/cardService';
import { CreateCard, RedeemCardRequest } from '@claude-code-manage/shared';

const cardService = new CardService();

export class CardController {
  /**
   * 创建卡密
   */
  async createCard(req: Request, res: Response, next: NextFunction) {
    try {
      const cardData: CreateCard = req.body;
      const result = await cardService.createCard(cardData);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量创建卡密
   */
  async createCards(req: Request, res: Response, next: NextFunction) {
    try {
      const { count, ...cardData }: CreateCard & { count: number } = req.body;
      const result = await cardService.createCards(cardData, count);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 兑换卡密
   */
  async redeemCard(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const redeemData: RedeemCardRequest = req.body;
      const result = await cardService.redeemCard(userId, redeemData);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取卡密列表
   */
  async getCards(req: Request, res: Response, next: NextFunction) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const filters = {
        type: req.query.type as string,
        isUsed: req.query.isUsed === 'true' ? true : req.query.isUsed === 'false' ? false : undefined,
        expired: req.query.expired === 'true' ? true : req.query.expired === 'false' ? false : undefined,
      };

      const result = await cardService.getCards(page, limit, filters);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除卡密
   */
  async deleteCard(req: Request, res: Response, next: NextFunction) {
    try {
      const cardId = req.params.id;
      const result = await cardService.deleteCard(cardId);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取卡密统计
   */
  async getCardStats(req: Request, res: Response, next: NextFunction) {
    try {
      const result = await cardService.getCardStats();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
