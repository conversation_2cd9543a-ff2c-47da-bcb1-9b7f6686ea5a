import { Router } from 'express';
import { <PERSON><PERSON><PERSON>roll<PERSON> } from '../controllers/claudeController';
import { validate } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { claudeRateLimit } from '../middleware/rateLimiter';
import { ClaudeRequestSchema } from '@claude-code-manage/shared';

const router = Router();
const claudeController = new ClaudeController();

// 发送Claude消息
router.post('/chat',
  authenticateToken,
  claudeRateLimit,
  validate(ClaudeRequestSchema),
  claudeController.sendMessage
);

// 获取可用模型列表
router.get('/models',
  claudeController.getModels
);

export default router;
