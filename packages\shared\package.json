{"name": "@claude-code-manage/shared", "version": "1.0.0", "description": "Shared types and utilities for Claude Code Management System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "jest": "^29.6.0", "rimraf": "^5.0.0", "typescript": "^5.1.0"}, "files": ["dist"]}