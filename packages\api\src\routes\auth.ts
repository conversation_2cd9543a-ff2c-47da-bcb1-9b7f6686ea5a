import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { validate } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { loginRateLimit, registerRateLimit } from '../middleware/rateLimiter';
import { LoginSchema, RegisterSchema } from '@claude-code-manage/shared';

const router = Router();
const authController = new AuthController();

// 登录
router.post('/login', 
  loginRateLimit,
  validate(LoginSchema),
  authController.login
);

// 注册
router.post('/register',
  registerRateLimit,
  validate(RegisterSchema),
  authController.register
);

// 登出
router.post('/logout',
  authController.logout
);

// 获取用户信息
router.get('/profile',
  authenticateToken,
  authController.getProfile
);

// 更新用户信息
router.put('/profile',
  authenticateToken,
  authController.updateProfile
);

// 验证token
router.post('/validate',
  authController.validateToken
);

export default router;
