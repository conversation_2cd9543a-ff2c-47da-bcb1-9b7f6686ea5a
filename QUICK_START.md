# 快速开始验证指南

## 🚀 快速验证步骤

### 1. 环境准备

确保你的系统已安装：
- Node.js 18+ 
- PostgreSQL 13+
- npm 9+

### 2. 项目设置

```bash
# 1. 安装依赖
npm run setup

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，至少配置以下项：
# DATABASE_URL=postgresql://username:password@localhost:5432/claude_code_manage
# JWT_SECRET=your-jwt-secret-key-at-least-32-characters
# CLAUDE_API_KEY=your-claude-api-key (可选，用于Claude功能测试)
```

### 3. 数据库初始化

```bash
cd packages/api

# 生成Prisma客户端
npm run db:generate

# 创建数据库表
npm run db:push

# 插入种子数据
npm run db:seed
```

### 4. 启动服务

```bash
# 回到根目录
cd ../..

# 启动API服务
npm run dev:api
```

API服务将在 http://localhost:3001 启动

### 5. 快速验证

在新的终端窗口中运行：

```bash
# 运行快速测试脚本
npm run quick-test
```

这个脚本会自动测试：
- ✅ API健康检查
- ✅ 用户注册功能
- ✅ 用户登录功能
- ✅ 获取用户信息
- ✅ Claude模型列表
- ✅ 认证中间件
- ✅ 限流功能

### 6. 手动验证

如果快速测试脚本运行成功，你也可以手动验证：

#### 6.1 健康检查
```bash
curl http://localhost:3001/api/health
```

#### 6.2 用户注册
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123"
  }'
```

#### 6.3 用户登录
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

保存返回的token，用于后续请求。

#### 6.4 获取用户信息
```bash
curl -X GET http://localhost:3001/api/auth/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 7. 运行单元测试

```bash
cd packages/api
npm test
```

## 🎯 验证成功标志

如果看到以下结果，说明后端API验证成功：

1. **快速测试脚本**显示：`🎉 所有测试都通过了！`
2. **健康检查**返回：`{"success": true, "message": "API服务运行正常"}`
3. **用户注册**返回：`{"success": true, "data": {...}, "message": "注册成功"}`
4. **用户登录**返回：`{"success": true, "data": {"token": "...", "user": {...}}}`
5. **单元测试**显示：所有测试用例通过

## 🔧 常见问题解决

### 数据库连接失败
```bash
# 检查PostgreSQL是否运行
# Windows:
net start postgresql-x64-13

# macOS:
brew services start postgresql

# Linux:
sudo systemctl start postgresql
```

### 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :3001  # Windows
lsof -i :3001                 # macOS/Linux

# 修改端口（在.env文件中）
API_PORT=3002
```

### 依赖安装失败
```bash
# 清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 📝 下一步

验证成功后，你可以：

1. **开发CLI工具**：`cd packages/cli && npm run dev`
2. **开发Web前端**：`cd packages/web && npm run dev`
3. **开发管理后台**：`cd packages/admin && npm run dev`
4. **查看完整测试指南**：`docs/testing-guide.md`

## 🆘 获取帮助

如果遇到问题：

1. 检查 `logs/` 目录下的日志文件
2. 查看控制台错误信息
3. 参考 `docs/testing-guide.md` 详细文档
4. 检查环境变量配置是否正确

---

**恭喜！** 🎉 如果快速验证通过，说明你的Claude Code Management系统后端已经成功搭建并运行！
