import { Router } from 'express';
import authRoutes from './auth';
import claudeRoutes from './claude';
import cardRoutes from './cards';

const router = Router();

// API路由
router.use('/auth', authRoutes);
router.use('/claude', claudeRoutes);
router.use('/cards', cardRoutes);

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
  });
});

export default router;
