import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService';
import { LoginRequest, RegisterRequest } from '@claude-code-manage/shared';

const authService = new AuthService();

export class AuthController {
  /**
   * 用户登录
   */
  async login(req: Request, res: Response, next: NextFunction) {
    try {
      const loginData: LoginRequest = req.body;
      const result = await authService.login(loginData);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户注册
   */
  async register(req: Request, res: Response, next: NextFunction) {
    try {
      const registerData: RegisterRequest = req.body;
      const result = await authService.register(registerData);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户登出
   */
  async logout(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];
      
      if (token) {
        await authService.logout(token);
      }
      
      res.json({ success: true, message: '登出成功' });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户信息
   */
  async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const result = await authService.getProfile(userId);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const updateData = req.body;
      const result = await authService.updateProfile(userId, updateData);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 验证token
   */
  async validateToken(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: '未提供认证令牌',
        });
      }
      
      const result = await authService.validateToken(token);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
