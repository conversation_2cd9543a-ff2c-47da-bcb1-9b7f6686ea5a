import { z } from 'zod';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 环境变量验证schema
const envSchema = z.object({
  // 基础配置
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  API_PORT: z.string().transform(Number).default('3001'),
  
  // 数据库配置
  DATABASE_URL: z.string().min(1, '数据库URL不能为空'),
  
  // JWT配置
  JWT_SECRET: z.string().min(32, 'JWT密钥长度至少32位'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('30d'),
  
  // Claude API配置
  CLAUDE_API_KEY: z.string().min(1, 'Claude API密钥不能为空'),
  CLAUDE_API_BASE_URL: z.string().url().default('https://api.anthropic.com'),
  
  // Redis配置
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // 邮件配置
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().transform(Number).default('587'),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  
  // 管理员配置
  ADMIN_EMAIL: z.string().email().default('<EMAIL>'),
  ADMIN_PASSWORD: z.string().min(6).default('admin123456'),
  
  // 应用配置
  APP_NAME: z.string().default('Claude Code Management'),
  APP_URL: z.string().url().default('http://localhost:3000'),
  API_URL: z.string().url().default('http://localhost:3001'),
  
  // 安全配置
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  SESSION_SECRET: z.string().min(32).default('your-session-secret-key'),
  
  // 限流配置
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15分钟
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Claude使用限制
  DEFAULT_DAILY_LIMIT: z.string().transform(Number).default('1000'),
  DEFAULT_MONTHLY_LIMIT: z.string().transform(Number).default('30000'),
  
  // 文件上传配置
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  UPLOAD_DIR: z.string().default('./uploads'),
  
  // 日志配置
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
});

// 验证环境变量
const envValidation = envSchema.safeParse(process.env);

if (!envValidation.success) {
  console.error('环境变量验证失败:');
  console.error(envValidation.error.format());
  process.exit(1);
}

export const env = envValidation.data;

// 导出常用配置
export const config = {
  // 服务器配置
  server: {
    port: env.API_PORT,
    env: env.NODE_ENV,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
  },
  
  // 数据库配置
  database: {
    url: env.DATABASE_URL,
  },
  
  // JWT配置
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  
  // Claude配置
  claude: {
    apiKey: env.CLAUDE_API_KEY,
    baseUrl: env.CLAUDE_API_BASE_URL,
  },
  
  // Redis配置
  redis: {
    url: env.REDIS_URL,
  },
  
  // 邮件配置
  smtp: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
  
  // 管理员配置
  admin: {
    email: env.ADMIN_EMAIL,
    password: env.ADMIN_PASSWORD,
  },
  
  // 应用配置
  app: {
    name: env.APP_NAME,
    url: env.APP_URL,
    apiUrl: env.API_URL,
  },
  
  // 安全配置
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    sessionSecret: env.SESSION_SECRET,
  },
  
  // 限流配置
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },
  
  // 使用限制配置
  limits: {
    defaultDaily: env.DEFAULT_DAILY_LIMIT,
    defaultMonthly: env.DEFAULT_MONTHLY_LIMIT,
  },
  
  // 文件配置
  file: {
    maxSize: env.MAX_FILE_SIZE,
    uploadDir: env.UPLOAD_DIR,
  },
  
  // 日志配置
  log: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
  },
};
