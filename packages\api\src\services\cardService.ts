import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { 
  CreateCard,
  RedeemCardRequest,
  createSuccessResponse,
  generateCardCode,
  CARD_VALUES,
} from '@claude-code-manage/shared';
import { BusinessError, NotFoundError } from '../middleware/errorHandler';

export class CardService {
  /**
   * 创建卡密
   */
  async createCard(cardData: CreateCard) {
    const { type, value, expiresAt } = cardData;
    
    // 生成唯一的卡密代码
    let code: string;
    let isUnique = false;
    let attempts = 0;
    
    while (!isUnique && attempts < 10) {
      code = generateCardCode();
      const existingCard = await prisma.card.findUnique({
        where: { code },
      });
      
      if (!existingCard) {
        isUnique = true;
      }
      attempts++;
    }
    
    if (!isUnique) {
      throw new BusinessError('生成卡密失败，请重试');
    }

    const card = await prisma.card.create({
      data: {
        code: code!,
        type,
        value: value || CARD_VALUES[type],
        expiresAt,
      },
    });

    logger.info('卡密创建成功:', { cardId: card.id, code: card.code, type: card.type });

    return createSuccessResponse({
      id: card.id,
      code: card.code,
      type: card.type,
      value: card.value,
      expiresAt: card.expiresAt,
      createdAt: card.createdAt,
    }, '卡密创建成功');
  }

  /**
   * 批量创建卡密
   */
  async createCards(cardData: CreateCard, count: number) {
    if (count <= 0 || count > 100) {
      throw new BusinessError('批量创建数量必须在1-100之间');
    }

    const cards = [];
    const { type, value, expiresAt } = cardData;

    for (let i = 0; i < count; i++) {
      // 生成唯一的卡密代码
      let code: string;
      let isUnique = false;
      let attempts = 0;
      
      while (!isUnique && attempts < 10) {
        code = generateCardCode();
        const existingCard = await prisma.card.findUnique({
          where: { code },
        });
        
        if (!existingCard) {
          isUnique = true;
        }
        attempts++;
      }
      
      if (!isUnique) {
        throw new BusinessError(`生成第${i + 1}个卡密失败，请重试`);
      }

      cards.push({
        code: code!,
        type,
        value: value || CARD_VALUES[type],
        expiresAt,
      });
    }

    const createdCards = await prisma.card.createMany({
      data: cards,
    });

    logger.info('批量卡密创建成功:', { count: createdCards.count, type });

    return createSuccessResponse({
      count: createdCards.count,
      cards: cards.map(card => ({
        code: card.code,
        type: card.type,
        value: card.value,
        expiresAt: card.expiresAt,
      })),
    }, `成功创建${createdCards.count}个卡密`);
  }

  /**
   * 兑换卡密
   */
  async redeemCard(userId: string, redeemData: RedeemCardRequest) {
    const { code } = redeemData;

    // 查找卡密
    const card = await prisma.card.findUnique({
      where: { code },
    });

    if (!card) {
      throw new NotFoundError('卡密不存在');
    }

    // 检查卡密是否已被使用
    if (card.isUsed) {
      throw new BusinessError('卡密已被使用');
    }

    // 检查卡密是否过期
    if (card.expiresAt && card.expiresAt < new Date()) {
      throw new BusinessError('卡密已过期');
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 计算新的限制值
    let newDailyLimit = user.dailyLimit;
    let newMonthlyLimit = user.monthlyLimit;

    switch (card.type) {
      case 'DAILY':
        newDailyLimit += card.value;
        break;
      case 'MONTHLY':
        newMonthlyLimit += card.value;
        break;
      case 'YEARLY':
        newMonthlyLimit += card.value;
        break;
    }

    // 使用事务更新用户限制和卡密状态
    const result = await prisma.$transaction(async (tx) => {
      // 更新用户限制
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          dailyLimit: newDailyLimit,
          monthlyLimit: newMonthlyLimit,
        },
      });

      // 标记卡密为已使用
      const updatedCard = await tx.card.update({
        where: { id: card.id },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date(),
        },
      });

      return { updatedUser, updatedCard };
    });

    logger.info('卡密兑换成功:', {
      userId,
      cardId: card.id,
      code: card.code,
      type: card.type,
      value: card.value,
    });

    return createSuccessResponse({
      card: {
        code: result.updatedCard.code,
        type: result.updatedCard.type,
        value: result.updatedCard.value,
        usedAt: result.updatedCard.usedAt,
      },
      user: {
        dailyLimit: result.updatedUser.dailyLimit,
        monthlyLimit: result.updatedUser.monthlyLimit,
      },
    }, '卡密兑换成功');
  }

  /**
   * 获取卡密列表
   */
  async getCards(page: number = 1, limit: number = 20, filters?: {
    type?: string;
    isUsed?: boolean;
    expired?: boolean;
  }) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.isUsed !== undefined) {
      where.isUsed = filters.isUsed;
    }

    if (filters?.expired !== undefined) {
      if (filters.expired) {
        where.expiresAt = {
          lt: new Date(),
        };
      } else {
        where.OR = [
          { expiresAt: null },
          { expiresAt: { gte: new Date() } },
        ];
      }
    }

    const [cards, total] = await Promise.all([
      prisma.card.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          redeemer: {
            select: {
              id: true,
              email: true,
              username: true,
            },
          },
        },
      }),
      prisma.card.count({ where }),
    ]);

    return createSuccessResponse({
      cards,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  }

  /**
   * 删除卡密
   */
  async deleteCard(cardId: string) {
    const card = await prisma.card.findUnique({
      where: { id: cardId },
    });

    if (!card) {
      throw new NotFoundError('卡密不存在');
    }

    if (card.isUsed) {
      throw new BusinessError('已使用的卡密不能删除');
    }

    await prisma.card.delete({
      where: { id: cardId },
    });

    logger.info('卡密删除成功:', { cardId, code: card.code });

    return createSuccessResponse(null, '卡密删除成功');
  }

  /**
   * 获取卡密统计
   */
  async getCardStats() {
    const [total, used, expired, byType] = await Promise.all([
      prisma.card.count(),
      prisma.card.count({ where: { isUsed: true } }),
      prisma.card.count({
        where: {
          expiresAt: { lt: new Date() },
          isUsed: false,
        },
      }),
      prisma.card.groupBy({
        by: ['type'],
        _count: true,
      }),
    ]);

    const typeStats = byType.reduce((acc, item) => {
      acc[item.type] = item._count;
      return acc;
    }, {} as Record<string, number>);

    return createSuccessResponse({
      total,
      used,
      unused: total - used,
      expired,
      byType: typeStats,
    });
  }
}
