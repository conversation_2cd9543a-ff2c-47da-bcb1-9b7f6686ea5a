// API端点常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    PROFILE: '/api/auth/profile',
  },
  
  // 用户相关
  USERS: {
    LIST: '/api/users',
    CREATE: '/api/users',
    UPDATE: '/api/users/:id',
    DELETE: '/api/users/:id',
    GET: '/api/users/:id',
  },
  
  // Claude相关
  CLAUDE: {
    CHAT: '/api/claude/chat',
    MODELS: '/api/claude/models',
  },
  
  // 卡密相关
  CARDS: {
    LIST: '/api/cards',
    CREATE: '/api/cards',
    REDEEM: '/api/cards/redeem',
    DELETE: '/api/cards/:id',
  },
  
  // 使用记录相关
  USAGE: {
    LIST: '/api/usage',
    STATS: '/api/usage/stats',
    USER_STATS: '/api/usage/user/:userId',
  },
  
  // 管理员相关
  ADMIN: {
    STATS: '/api/admin/stats',
    USERS: '/api/admin/users',
    CARDS: '/api/admin/cards',
    USAGE: '/api/admin/usage',
  },
};

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

// 用户角色
export const USER_ROLES = {
  USER: 'user',
  ADMIN: 'admin',
} as const;

// 卡密类型
export const CARD_TYPES = {
  DAILY: 'daily',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
} as const;

// Claude模型
export const CLAUDE_MODELS = {
  SONNET: 'claude-3-sonnet-20240229',
  HAIKU: 'claude-3-haiku-20240307',
  OPUS: 'claude-3-opus-20240229',
} as const;

// 默认限制
export const DEFAULT_LIMITS = {
  DAILY: 1000,
  MONTHLY: 30000,
  YEARLY: 365000,
};

// 卡密价值
export const CARD_VALUES = {
  [CARD_TYPES.DAILY]: 1000,
  [CARD_TYPES.MONTHLY]: 30000,
  [CARD_TYPES.YEARLY]: 365000,
};

// JWT配置
export const JWT_CONFIG = {
  EXPIRES_IN: '7d',
  REFRESH_EXPIRES_IN: '30d',
  ALGORITHM: 'HS256',
};

// 限流配置
export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 15 * 60 * 1000, // 15分钟
  MAX_REQUESTS: 100, // 每个窗口最大请求数
  CLAUDE_WINDOW_MS: 60 * 1000, // 1分钟
  CLAUDE_MAX_REQUESTS: 10, // Claude API每分钟最大请求数
};

// 错误消息
export const ERROR_MESSAGES = {
  // 通用错误
  INTERNAL_ERROR: '服务器内部错误',
  INVALID_REQUEST: '请求参数无效',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '禁止访问',
  NOT_FOUND: '资源不存在',
  TOO_MANY_REQUESTS: '请求过于频繁，请稍后再试',
  
  // 认证错误
  INVALID_CREDENTIALS: '邮箱或密码错误',
  EMAIL_ALREADY_EXISTS: '邮箱已存在',
  USERNAME_ALREADY_EXISTS: '用户名已存在',
  INVALID_TOKEN: '无效的令牌',
  TOKEN_EXPIRED: '令牌已过期',
  
  // 用户错误
  USER_NOT_FOUND: '用户不存在',
  USER_INACTIVE: '用户已被禁用',
  INSUFFICIENT_QUOTA: '配额不足',
  
  // 卡密错误
  CARD_NOT_FOUND: '卡密不存在',
  CARD_ALREADY_USED: '卡密已被使用',
  CARD_EXPIRED: '卡密已过期',
  INVALID_CARD_CODE: '无效的卡密代码',
  
  // Claude错误
  CLAUDE_API_ERROR: 'Claude API错误',
  CLAUDE_QUOTA_EXCEEDED: 'Claude使用配额已超限',
  INVALID_MODEL: '无效的模型',
};

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '退出成功',
  PROFILE_UPDATED: '个人信息更新成功',
  CARD_REDEEMED: '卡密兑换成功',
  CARD_CREATED: '卡密创建成功',
  USER_CREATED: '用户创建成功',
  USER_UPDATED: '用户信息更新成功',
  USER_DELETED: '用户删除成功',
};

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  USERNAME: /^[a-zA-Z0-9_]{3,50}$/,
  CARD_CODE: /^CC[A-Z0-9]{12}$/,
};

// 文件配置
export const FILE_CONFIG = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'text/plain'],
  UPLOAD_DIR: './uploads',
};

// 日志级别
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
} as const;
