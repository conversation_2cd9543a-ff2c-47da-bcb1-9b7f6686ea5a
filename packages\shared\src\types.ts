import { z } from 'zod';

// 用户相关类型
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  username: z.string().min(3).max(50),
  password: z.string().min(6),
  role: z.enum(['user', 'admin']),
  isActive: z.boolean(),
  dailyLimit: z.number().int().positive(),
  monthlyLimit: z.number().int().positive(),
  dailyUsed: z.number().int().nonnegative(),
  monthlyUsed: z.number().int().nonnegative(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

export const CreateUserSchema = UserSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  dailyUsed: true,
  monthlyUsed: true,
});

export type CreateUser = z.infer<typeof CreateUserSchema>;

export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export type LoginRequest = z.infer<typeof LoginSchema>;

export const RegisterSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(50),
  password: z.string().min(6),
});

export type RegisterRequest = z.infer<typeof RegisterSchema>;

// 卡密相关类型
export const CardSchema = z.object({
  id: z.string(),
  code: z.string(),
  type: z.enum(['daily', 'monthly', 'yearly']),
  value: z.number().int().positive(), // 增加的使用量
  isUsed: z.boolean(),
  usedBy: z.string().optional(),
  usedAt: z.date().optional(),
  createdAt: z.date(),
  expiresAt: z.date().optional(),
});

export type Card = z.infer<typeof CardSchema>;

export const CreateCardSchema = CardSchema.omit({
  id: true,
  isUsed: true,
  usedBy: true,
  usedAt: true,
  createdAt: true,
});

export type CreateCard = z.infer<typeof CreateCardSchema>;

export const RedeemCardSchema = z.object({
  code: z.string(),
});

export type RedeemCardRequest = z.infer<typeof RedeemCardSchema>;

// Claude使用记录类型
export const UsageRecordSchema = z.object({
  id: z.string(),
  userId: z.string(),
  model: z.string(),
  inputTokens: z.number().int().nonnegative(),
  outputTokens: z.number().int().nonnegative(),
  totalTokens: z.number().int().nonnegative(),
  cost: z.number().nonnegative(),
  requestId: z.string(),
  createdAt: z.date(),
});

export type UsageRecord = z.infer<typeof UsageRecordSchema>;

// Claude请求类型
export const ClaudeMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
});

export type ClaudeMessage = z.infer<typeof ClaudeMessageSchema>;

export const ClaudeRequestSchema = z.object({
  model: z.string().default('claude-3-sonnet-20240229'),
  messages: z.array(ClaudeMessageSchema),
  max_tokens: z.number().int().positive().default(4096),
  temperature: z.number().min(0).max(1).default(0.7),
  stream: z.boolean().default(false),
});

export type ClaudeRequest = z.infer<typeof ClaudeRequestSchema>;

// API响应类型
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
  error: z.string().optional(),
});

export type ApiResponse<T = any> = {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
};

// JWT Token类型
export const TokenPayloadSchema = z.object({
  userId: z.string(),
  email: z.string(),
  role: z.enum(['user', 'admin']),
  iat: z.number(),
  exp: z.number(),
});

export type TokenPayload = z.infer<typeof TokenPayloadSchema>;

// 统计数据类型
export const StatsSchema = z.object({
  totalUsers: z.number().int().nonnegative(),
  activeUsers: z.number().int().nonnegative(),
  totalRequests: z.number().int().nonnegative(),
  totalTokens: z.number().int().nonnegative(),
  totalCost: z.number().nonnegative(),
});

export type Stats = z.infer<typeof StatsSchema>;

// 配置类型
export const ConfigSchema = z.object({
  claudeApiKey: z.string(),
  claudeApiBaseUrl: z.string(),
  jwtSecret: z.string(),
  databaseUrl: z.string(),
  redisUrl: z.string(),
  smtpConfig: z.object({
    host: z.string(),
    port: z.number().int().positive(),
    user: z.string(),
    pass: z.string(),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;
