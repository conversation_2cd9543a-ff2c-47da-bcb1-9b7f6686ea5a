import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { logger } from '../utils/logger';
import { createErrorResponse, TokenPayload } from '@claude-code-manage/shared';

// 扩展Request类型以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json(
        createErrorResponse('未提供认证令牌', '请提供有效的认证令牌')
      );
    }

    // 验证JWT token
    const decoded = jwt.verify(token, config.jwt.secret) as TokenPayload;

    // 检查token是否在数据库中存在且未过期
    const session = await prisma.session.findUnique({
      where: { token },
      include: { user: true },
    });

    if (!session || session.expiresAt < new Date()) {
      return res.status(401).json(
        createErrorResponse('令牌已过期或无效', '请重新登录')
      );
    }

    // 检查用户是否仍然活跃
    if (!session.user.isActive) {
      return res.status(403).json(
        createErrorResponse('用户账户已被禁用', '请联系管理员')
      );
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: session.user.id,
      email: session.user.email,
      role: session.user.role,
    };

    next();
  } catch (error) {
    logger.error('JWT认证失败:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json(
        createErrorResponse('无效的令牌', '请重新登录')
      );
    }

    return res.status(500).json(
      createErrorResponse('认证服务错误', '请稍后重试')
    );
  }
};

/**
 * 管理员权限检查中间件
 */
export const requireAdmin = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return res.status(401).json(
      createErrorResponse('未认证', '请先登录')
    );
  }

  if (req.user.role !== 'ADMIN') {
    return res.status(403).json(
      createErrorResponse('权限不足', '需要管理员权限')
    );
  }

  next();
};

/**
 * 可选认证中间件（不强制要求认证）
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret) as TokenPayload;
      
      const session = await prisma.session.findUnique({
        where: { token },
        include: { user: true },
      });

      if (session && session.expiresAt >= new Date() && session.user.isActive) {
        req.user = {
          id: session.user.id,
          email: session.user.email,
          role: session.user.role,
        };
      }
    }

    next();
  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    logger.debug('可选认证失败:', error);
    next();
  }
};
