import { PrismaClient, Role, CardType } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { generateCardCode } from '@claude-code-manage/shared';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子数据初始化...');

  // 创建管理员用户
  const adminPassword = await bcrypt.hash('admin123456', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: adminPassword,
      role: Role.ADMIN,
      dailyLimit: 100000,
      monthlyLimit: 3000000,
    },
  });

  console.log('管理员用户创建成功:', admin.email);

  // 创建测试用户
  const testUserPassword = await bcrypt.hash('test123456', 12);
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'testuser',
      password: testUserPassword,
      role: Role.USER,
      dailyLimit: 1000,
      monthlyLimit: 30000,
    },
  });

  console.log('测试用户创建成功:', testUser.email);

  // 创建一些测试卡密
  const cards = [
    {
      code: generateCardCode(),
      type: CardType.DAILY,
      value: 1000,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
    },
    {
      code: generateCardCode(),
      type: CardType.MONTHLY,
      value: 30000,
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90天后过期
    },
    {
      code: generateCardCode(),
      type: CardType.YEARLY,
      value: 365000,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 365天后过期
    },
  ];

  for (const cardData of cards) {
    const card = await prisma.card.create({
      data: cardData,
    });
    console.log(`创建卡密: ${card.code} (类型: ${card.type}, 价值: ${card.value})`);
  }

  // 创建系统配置
  const configs = [
    {
      key: 'claude_api_base_url',
      value: 'https://api.anthropic.com',
    },
    {
      key: 'default_daily_limit',
      value: '1000',
    },
    {
      key: 'default_monthly_limit',
      value: '30000',
    },
    {
      key: 'rate_limit_enabled',
      value: 'true',
    },
    {
      key: 'registration_enabled',
      value: 'true',
    },
  ];

  for (const configData of configs) {
    await prisma.systemConfig.upsert({
      where: { key: configData.key },
      update: { value: configData.value },
      create: configData,
    });
    console.log(`创建系统配置: ${configData.key} = ${configData.value}`);
  }

  console.log('数据库种子数据初始化完成!');
}

main()
  .catch((e) => {
    console.error('数据库种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
