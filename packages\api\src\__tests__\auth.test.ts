import request from 'supertest';
import bcrypt from 'bcryptjs';
import app from '../app';
import { prisma } from './setup';

describe('Auth API', () => {
  describe('POST /api/auth/register', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.username).toBe(userData.username);
      expect(response.body.data.user).not.toHaveProperty('password');

      // 验证用户已保存到数据库
      const user = await prisma.user.findUnique({
        where: { email: userData.email },
      });
      expect(user).toBeTruthy();
      expect(user?.email).toBe(userData.email);
    });

    it('应该拒绝重复的邮箱', async () => {
      // 先创建一个用户
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'testuser1',
          password: await bcrypt.hash('password123', 12),
        },
      });

      const userData = {
        email: '<EMAIL>',
        username: 'testuser2',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('邮箱已被注册');
    });

    it('应该验证请求参数', async () => {
      const invalidData = {
        email: 'invalid-email',
        username: 'ab', // 太短
        password: '123', // 太短
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('请求参数验证失败');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // 创建测试用户
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'testuser',
          password: await bcrypt.hash('password123', 12),
        },
      });
    });

    it('应该成功登录', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeTruthy();
      expect(response.body.data.user.email).toBe(loginData.email);

      // 验证会话已保存到数据库
      const session = await prisma.session.findUnique({
        where: { token: response.body.data.token },
      });
      expect(session).toBeTruthy();
    });

    it('应该拒绝错误的密码', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('邮箱或密码错误');
    });

    it('应该拒绝不存在的用户', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('用户不存在');
    });
  });

  describe('GET /api/auth/profile', () => {
    let authToken: string;
    let userId: string;

    beforeEach(async () => {
      // 创建测试用户并登录
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'testuser',
          password: await bcrypt.hash('password123', 12),
        },
      });
      userId = user.id;

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });

      authToken = loginResponse.body.data.token;
    });

    it('应该返回用户信息', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(userId);
      expect(response.body.data.email).toBe('<EMAIL>');
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('未提供认证令牌');
    });

    it('应该拒绝无效的token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('无效的令牌');
    });
  });

  describe('POST /api/auth/logout', () => {
    let authToken: string;

    beforeEach(async () => {
      // 创建测试用户并登录
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'testuser',
          password: await bcrypt.hash('password123', 12),
        },
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });

      authToken = loginResponse.body.data.token;
    });

    it('应该成功登出', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证会话已从数据库删除
      const session = await prisma.session.findUnique({
        where: { token: authToken },
      });
      expect(session).toBeNull();
    });
  });
});
