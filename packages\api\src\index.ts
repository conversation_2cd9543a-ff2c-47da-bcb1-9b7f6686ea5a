import app from './app';
import { config } from './config/env';
import { logger } from './utils/logger';
import { connectDatabase } from './config/database';

async function startServer() {
  try {
    // 连接数据库
    await connectDatabase();
    
    // 启动服务器
    const server = app.listen(config.server.port, () => {
      logger.info(`🚀 API服务器启动成功`);
      logger.info(`📍 服务地址: http://localhost:${config.server.port}`);
      logger.info(`🌍 环境: ${config.server.env}`);
      logger.info(`📊 健康检查: http://localhost:${config.server.port}/api/health`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`收到${signal}信号，开始优雅关闭服务器...`);
      
      server.close(() => {
        logger.info('HTTP服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', { reason, promise });
  process.exit(1);
});

// 启动服务器
startServer();
