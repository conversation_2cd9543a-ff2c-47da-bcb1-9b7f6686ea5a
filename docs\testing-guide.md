# 测试验证指南

本文档介绍如何验证和测试Claude Code Management系统的各个组件。

## 测试环境准备

### 1. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装各个包的依赖
npm run install:packages
```

### 2. 环境配置

复制环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```env
# 数据库配置（测试用）
DATABASE_URL=postgresql://username:password@localhost:5432/claude_code_test

# JWT密钥
JWT_SECRET=your-test-jwt-secret-key-at-least-32-characters

# Claude API配置（测试用）
CLAUDE_API_KEY=your-test-claude-api-key
CLAUDE_API_BASE_URL=https://api.anthropic.com

# 其他配置...
```

### 3. 数据库设置

```bash
# 进入API目录
cd packages/api

# 生成Prisma客户端
npm run db:generate

# 推送数据库schema
npm run db:push

# 运行种子数据
npm run db:seed
```

## 测试方法

### 1. 单元测试

运行所有单元测试：
```bash
# 在根目录运行
npm test

# 或者运行特定包的测试
npm run test:api
npm run test:web
npm run test:admin
npm run test:cli
```

运行API测试：
```bash
cd packages/api
npm test
```

运行特定测试文件：
```bash
cd packages/api
npm test -- auth.test.ts
```

运行测试并生成覆盖率报告：
```bash
cd packages/api
npm test -- --coverage
```

### 2. 集成测试

启动开发服务器：
```bash
# 在根目录运行所有服务
npm run dev

# 或者分别启动各个服务
npm run dev:api    # 启动API服务 (端口3001)
npm run dev:web    # 启动Web前端 (端口3000)
npm run dev:admin  # 启动管理后台 (端口3002)
```

### 3. API测试

#### 使用curl测试

1. **健康检查**
```bash
curl http://localhost:3001/api/health
```

2. **用户注册**
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123"
  }'
```

3. **用户登录**
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

4. **获取用户信息**
```bash
# 使用登录返回的token
curl -X GET http://localhost:3001/api/auth/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

5. **Claude聊天测试**
```bash
curl -X POST http://localhost:3001/api/claude/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 1000
  }'
```

6. **卡密兑换测试**
```bash
curl -X POST http://localhost:3001/api/cards/redeem \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "code": "CC123456789012"
  }'
```

#### 使用Postman测试

1. 导入API集合：创建一个Postman集合，包含所有API端点
2. 设置环境变量：`baseUrl`, `token`等
3. 创建测试脚本验证响应

### 4. 前端测试

启动前端开发服务器后，在浏览器中访问：

- 用户前端：http://localhost:3000
- 管理后台：http://localhost:3002

测试功能：
1. 用户注册/登录
2. 卡密兑换
3. Claude聊天
4. 使用记录查看
5. 管理员功能（用户管理、卡密管理等）

### 5. CLI工具测试

```bash
# 进入CLI目录
cd packages/cli

# 构建CLI工具
npm run build

# 全局安装（用于测试）
npm link

# 测试CLI命令
claude --help
claude login
claude chat "Hello, Claude!"
```

## 自动化测试脚本

创建测试脚本 `scripts/test.sh`：

```bash
#!/bin/bash

echo "🧪 开始运行测试..."

# 检查环境
echo "📋 检查环境配置..."
if [ ! -f .env ]; then
    echo "❌ 未找到.env文件，请先配置环境变量"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
npm run install:all

# 数据库设置
echo "🗄️ 设置测试数据库..."
cd packages/api
npm run db:generate
npm run db:push
npm run db:seed
cd ../..

# 运行单元测试
echo "🔬 运行单元测试..."
npm test

# 启动服务进行集成测试
echo "🚀 启动服务..."
npm run dev &
SERVER_PID=$!

# 等待服务启动
sleep 10

# 运行API测试
echo "🌐 测试API端点..."
curl -f http://localhost:3001/api/health || {
    echo "❌ API健康检查失败"
    kill $SERVER_PID
    exit 1
}

echo "✅ 所有测试通过！"

# 清理
kill $SERVER_PID
```

## 测试检查清单

### API测试
- [ ] 用户注册功能
- [ ] 用户登录功能
- [ ] JWT认证中间件
- [ ] 用户信息获取
- [ ] Claude聊天功能
- [ ] 卡密创建功能
- [ ] 卡密兑换功能
- [ ] 使用记录统计
- [ ] 限流中间件
- [ ] 错误处理

### 前端测试
- [ ] 用户界面响应式设计
- [ ] 登录/注册表单
- [ ] Claude聊天界面
- [ ] 卡密兑换界面
- [ ] 使用记录展示
- [ ] 管理后台功能

### CLI测试
- [ ] CLI安装和配置
- [ ] 用户认证
- [ ] Claude命令执行
- [ ] 错误处理

### 性能测试
- [ ] API响应时间
- [ ] 并发请求处理
- [ ] 数据库查询性能
- [ ] 内存使用情况

### 安全测试
- [ ] SQL注入防护
- [ ] XSS防护
- [ ] CSRF防护
- [ ] 认证绕过测试
- [ ] 权限控制测试

## 常见问题

### 1. 数据库连接失败
- 检查PostgreSQL服务是否运行
- 验证DATABASE_URL配置
- 确认数据库用户权限

### 2. Claude API调用失败
- 检查CLAUDE_API_KEY是否有效
- 验证网络连接
- 查看API配额限制

### 3. 测试超时
- 增加测试超时时间
- 检查服务启动时间
- 优化数据库查询

### 4. 端口冲突
- 检查端口是否被占用
- 修改配置文件中的端口设置
- 使用不同的端口进行测试

## 持续集成

可以配置GitHub Actions或其他CI/CD工具自动运行测试：

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: npm run install:all
    
    - name: Run tests
      run: npm test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
        JWT_SECRET: test-jwt-secret-key-for-ci-testing
```
