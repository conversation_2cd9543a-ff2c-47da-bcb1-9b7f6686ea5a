# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/claude_code_manage

# JWT密钥
JWT_SECRET=your-jwt-secret-key-change-this-in-production

# Claude API配置
CLAUDE_API_KEY=your-claude-api-key
CLAUDE_API_BASE_URL=https://api.anthropic.com

# 服务端口
API_PORT=3001
WEB_PORT=3000
ADMIN_PORT=3002

# Redis配置（用于缓存和会话）
REDIS_URL=redis://localhost:6379

# 邮件服务配置（用于用户注册验证）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 管理员默认账户
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456

# 应用配置
APP_NAME=Claude Code Management
APP_URL=http://localhost:3000
API_URL=http://localhost:3001

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Claude使用限制
DEFAULT_DAILY_LIMIT=1000
DEFAULT_MONTHLY_LIMIT=30000

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
