import axios from 'axios';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { logger } from '../utils/logger';
import { 
  ClaudeRequest, 
  ClaudeMessage,
  createSuccessResponse,
  createErrorResponse,
  calculateTokenCost,
} from '@claude-code-manage/shared';
import { BusinessError, NotFoundError } from '../middleware/errorHandler';

export class ClaudeService {
  private claudeClient;

  constructor() {
    this.claudeClient = axios.create({
      baseURL: config.claude.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.claude.apiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      timeout: 60000, // 60秒超时
    });
  }

  /**
   * 发送Claude请求
   */
  async sendMessage(userId: string, request: ClaudeRequest) {
    // 检查用户是否存在且活跃
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    if (!user.isActive) {
      throw new BusinessError('用户账户已被禁用');
    }

    // 检查用户配额
    if (user.dailyUsed >= user.dailyLimit) {
      throw new BusinessError('今日使用配额已用完');
    }

    if (user.monthlyUsed >= user.monthlyLimit) {
      throw new BusinessError('本月使用配额已用完');
    }

    try {
      // 发送请求到Claude API
      const response = await this.claudeClient.post('/v1/messages', {
        model: request.model,
        max_tokens: request.max_tokens,
        messages: request.messages,
        temperature: request.temperature,
        stream: request.stream,
      });

      const claudeResponse = response.data;
      
      // 计算token使用量
      const inputTokens = claudeResponse.usage?.input_tokens || 0;
      const outputTokens = claudeResponse.usage?.output_tokens || 0;
      const totalTokens = inputTokens + outputTokens;
      
      // 计算成本
      const cost = calculateTokenCost(inputTokens, outputTokens, request.model);

      // 记录使用情况
      await this.recordUsage(userId, {
        model: request.model,
        inputTokens,
        outputTokens,
        totalTokens,
        cost,
        requestId: claudeResponse.id,
        prompt: JSON.stringify(request.messages),
        response: JSON.stringify(claudeResponse.content),
      });

      // 更新用户使用量
      await this.updateUserUsage(userId, totalTokens);

      logger.info('Claude请求成功:', {
        userId,
        model: request.model,
        inputTokens,
        outputTokens,
        totalTokens,
        cost,
      });

      return createSuccessResponse({
        id: claudeResponse.id,
        content: claudeResponse.content,
        model: claudeResponse.model,
        role: claudeResponse.role,
        stop_reason: claudeResponse.stop_reason,
        stop_sequence: claudeResponse.stop_sequence,
        usage: {
          input_tokens: inputTokens,
          output_tokens: outputTokens,
          total_tokens: totalTokens,
          cost: cost,
        },
      });
    } catch (error: any) {
      logger.error('Claude API请求失败:', {
        userId,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      if (error.response?.status === 401) {
        throw new BusinessError('Claude API认证失败');
      }

      if (error.response?.status === 429) {
        throw new BusinessError('Claude API请求频率过高，请稍后重试');
      }

      if (error.response?.status === 400) {
        throw new BusinessError('请求参数错误: ' + (error.response.data?.error?.message || '未知错误'));
      }

      throw new BusinessError('Claude服务暂时不可用，请稍后重试');
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels() {
    const models = [
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        description: '平衡性能和成本的模型，适合大多数任务',
        pricing: {
          input: 0.003,
          output: 0.015,
        },
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        description: '快速且经济的模型，适合简单任务',
        pricing: {
          input: 0.00025,
          output: 0.00125,
        },
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        description: '最强大的模型，适合复杂任务',
        pricing: {
          input: 0.015,
          output: 0.075,
        },
      },
    ];

    return createSuccessResponse(models);
  }

  /**
   * 记录使用情况
   */
  private async recordUsage(userId: string, usageData: {
    model: string;
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    cost: number;
    requestId: string;
    prompt?: string;
    response?: string;
  }) {
    await prisma.usageRecord.create({
      data: {
        userId,
        model: usageData.model,
        inputTokens: usageData.inputTokens,
        outputTokens: usageData.outputTokens,
        totalTokens: usageData.totalTokens,
        cost: usageData.cost,
        requestId: usageData.requestId,
        prompt: usageData.prompt,
        response: usageData.response,
      },
    });
  }

  /**
   * 更新用户使用量
   */
  private async updateUserUsage(userId: string, tokens: number) {
    // 获取当前日期
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // 计算今日使用量
    const dailyUsage = await prisma.usageRecord.aggregate({
      where: {
        userId,
        createdAt: {
          gte: startOfDay,
        },
      },
      _sum: {
        totalTokens: true,
      },
    });

    // 计算本月使用量
    const monthlyUsage = await prisma.usageRecord.aggregate({
      where: {
        userId,
        createdAt: {
          gte: startOfMonth,
        },
      },
      _sum: {
        totalTokens: true,
      },
    });

    // 更新用户使用量
    await prisma.user.update({
      where: { id: userId },
      data: {
        dailyUsed: dailyUsage._sum.totalTokens || 0,
        monthlyUsed: monthlyUsage._sum.totalTokens || 0,
      },
    });
  }
}
