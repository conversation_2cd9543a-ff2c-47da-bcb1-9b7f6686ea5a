{"name": "@claude-code-manage/api", "version": "1.0.0", "description": "Backend API service for Claude Code Management System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@claude-code-manage/shared": "^1.0.0", "@prisma/client": "^5.0.0", "express": "^4.18.0", "express-rate-limit": "^6.8.0", "helmet": "^7.0.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "zod": "^3.22.0", "winston": "^3.10.0", "redis": "^4.6.0", "nodemailer": "^6.9.0", "axios": "^1.4.0", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.0", "cookie-parser": "^1.4.6", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/cors": "^2.8.0", "@types/nodemailer": "^6.4.0", "@types/multer": "^1.4.0", "@types/cookie-parser": "^1.4.0", "@types/compression": "^1.7.0", "@types/morgan": "^1.9.0", "@types/jest": "^29.5.0", "jest": "^29.6.0", "ts-jest": "^29.1.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "prisma": "^5.0.0", "typescript": "^5.1.0", "rimraf": "^5.0.0"}}