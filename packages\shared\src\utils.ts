import { ApiResponse } from './types';

/**
 * 创建成功的API响应
 */
export function createSuccessResponse<T>(data?: T, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
  };
}

/**
 * 创建错误的API响应
 */
export function createErrorResponse(error: string, message?: string): ApiResponse {
  return {
    success: false,
    error,
    message,
  };
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成卡密代码
 */
export function generateCardCode(): string {
  const prefix = 'CC';
  const randomPart = generateRandomString(12);
  return `${prefix}${randomPart}`.toUpperCase();
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 格式化日期
 */
export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * 格式化日期时间
 */
export function formatDateTime(date: Date): string {
  return date.toISOString().replace('T', ' ').split('.')[0];
}

/**
 * 计算token成本（基于Claude定价）
 */
export function calculateTokenCost(inputTokens: number, outputTokens: number, model: string): number {
  // Claude 3 Sonnet定价（每1000 tokens）
  const pricing = {
    'claude-3-sonnet-20240229': {
      input: 0.003,  // $0.003 per 1K input tokens
      output: 0.015, // $0.015 per 1K output tokens
    },
    'claude-3-haiku-20240307': {
      input: 0.00025,
      output: 0.00125,
    },
    'claude-3-opus-20240229': {
      input: 0.015,
      output: 0.075,
    },
  };

  const modelPricing = pricing[model as keyof typeof pricing] || pricing['claude-3-sonnet-20240229'];
  
  const inputCost = (inputTokens / 1000) * modelPricing.input;
  const outputCost = (outputTokens / 1000) * modelPricing.output;
  
  return inputCost + outputCost;
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

/**
 * 截断字符串
 */
export function truncateString(str: string, maxLength: number): string {
  if (str.length <= maxLength) {
    return str;
  }
  return str.substring(0, maxLength - 3) + '...';
}

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 6) {
    errors.push('密码长度至少6位');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
