#!/usr/bin/env node

const axios = require('axios');
const chalk = require('chalk');

const API_BASE_URL = 'http://localhost:3001/api';

class QuickTester {
  constructor() {
    this.token = null;
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}]`;
    
    switch (type) {
      case 'success':
        console.log(chalk.green(`${prefix} ✅ ${message}`));
        break;
      case 'error':
        console.log(chalk.red(`${prefix} ❌ ${message}`));
        break;
      case 'warning':
        console.log(chalk.yellow(`${prefix} ⚠️  ${message}`));
        break;
      default:
        console.log(chalk.blue(`${prefix} ℹ️  ${message}`));
    }
  }

  async test(name, testFn) {
    try {
      this.log(`开始测试: ${name}`);
      await testFn();
      this.log(`测试通过: ${name}`, 'success');
      this.testResults.push({ name, status: 'passed' });
    } catch (error) {
      this.log(`测试失败: ${name} - ${error.message}`, 'error');
      this.testResults.push({ name, status: 'failed', error: error.message });
    }
  }

  async healthCheck() {
    const response = await axios.get(`${API_BASE_URL}/health`);
    if (!response.data.success) {
      throw new Error('健康检查失败');
    }
  }

  async registerUser() {
    const userData = {
      email: `test${Date.now()}@example.com`,
      username: `testuser${Date.now()}`,
      password: 'password123'
    };

    const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
    if (!response.data.success) {
      throw new Error('用户注册失败');
    }
    
    this.testUser = userData;
  }

  async loginUser() {
    if (!this.testUser) {
      throw new Error('没有测试用户');
    }

    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: this.testUser.email,
      password: this.testUser.password
    });

    if (!response.data.success || !response.data.data.token) {
      throw new Error('用户登录失败');
    }

    this.token = response.data.data.token;
  }

  async getUserProfile() {
    if (!this.token) {
      throw new Error('没有认证token');
    }

    const response = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${this.token}` }
    });

    if (!response.data.success) {
      throw new Error('获取用户信息失败');
    }
  }

  async getClaudeModels() {
    const response = await axios.get(`${API_BASE_URL}/claude/models`);
    if (!response.data.success || !Array.isArray(response.data.data)) {
      throw new Error('获取Claude模型列表失败');
    }
  }

  async testClaudeChat() {
    if (!this.token) {
      throw new Error('没有认证token');
    }

    // 注意：这个测试需要有效的Claude API密钥
    try {
      const response = await axios.post(`${API_BASE_URL}/claude/chat`, {
        model: 'claude-3-sonnet-20240229',
        messages: [
          { role: 'user', content: 'Hello, this is a test message.' }
        ],
        max_tokens: 100
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });

      if (!response.data.success) {
        throw new Error('Claude聊天测试失败');
      }
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.error?.includes('Claude')) {
        this.log('Claude API密钥可能无效，跳过聊天测试', 'warning');
        return;
      }
      throw error;
    }
  }

  async testInvalidAuth() {
    try {
      await axios.get(`${API_BASE_URL}/auth/profile`, {
        headers: { Authorization: 'Bearer invalid-token' }
      });
      throw new Error('应该拒绝无效token');
    } catch (error) {
      if (error.response?.status !== 401) {
        throw new Error('无效token测试失败');
      }
    }
  }

  async testRateLimit() {
    // 快速发送多个请求测试限流
    const promises = Array(10).fill().map(() => 
      axios.get(`${API_BASE_URL}/health`)
    );

    try {
      await Promise.all(promises);
      this.log('限流测试：未触发限制（正常情况）', 'warning');
    } catch (error) {
      if (error.response?.status === 429) {
        this.log('限流测试：成功触发限制', 'success');
      } else {
        throw new Error('限流测试失败');
      }
    }
  }

  async runAllTests() {
    console.log(chalk.cyan('\n🚀 开始快速验证测试...\n'));

    await this.test('API健康检查', () => this.healthCheck());
    await this.test('用户注册', () => this.registerUser());
    await this.test('用户登录', () => this.loginUser());
    await this.test('获取用户信息', () => this.getUserProfile());
    await this.test('获取Claude模型列表', () => this.getClaudeModels());
    await this.test('Claude聊天功能', () => this.testClaudeChat());
    await this.test('无效认证测试', () => this.testInvalidAuth());
    await this.test('限流测试', () => this.testRateLimit());

    this.printSummary();
  }

  printSummary() {
    console.log(chalk.cyan('\n📊 测试结果汇总:\n'));
    
    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const failed = this.testResults.filter(r => r.status === 'failed').length;
    const total = this.testResults.length;

    console.log(chalk.green(`✅ 通过: ${passed}/${total}`));
    console.log(chalk.red(`❌ 失败: ${failed}/${total}`));

    if (failed > 0) {
      console.log(chalk.red('\n失败的测试:'));
      this.testResults
        .filter(r => r.status === 'failed')
        .forEach(r => {
          console.log(chalk.red(`  - ${r.name}: ${r.error}`));
        });
    }

    console.log(chalk.cyan('\n测试完成！'));
    
    if (failed === 0) {
      console.log(chalk.green('🎉 所有测试都通过了！'));
      process.exit(0);
    } else {
      console.log(chalk.red('💥 有测试失败，请检查上述错误信息。'));
      process.exit(1);
    }
  }
}

// 检查服务是否运行
async function checkServer() {
  try {
    await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log(chalk.cyan('🔍 检查API服务是否运行...'));
  
  const isServerRunning = await checkServer();
  if (!isServerRunning) {
    console.log(chalk.red('❌ API服务未运行！'));
    console.log(chalk.yellow('请先启动API服务：'));
    console.log(chalk.white('  cd packages/api && npm run dev'));
    console.log(chalk.white('  或者在根目录运行：npm run dev:api'));
    process.exit(1);
  }

  console.log(chalk.green('✅ API服务正在运行'));

  const tester = new QuickTester();
  await tester.runAllTests();
}

// 运行测试
main().catch(error => {
  console.error(chalk.red('测试运行出错:'), error.message);
  process.exit(1);
});
