import { Request, Response, NextFunction } from 'express';
import { ClaudeService } from '../services/claudeService';
import { ClaudeRequest } from '@claude-code-manage/shared';

const claudeService = new ClaudeService();

export class ClaudeController {
  /**
   * 发送Claude消息
   */
  async sendMessage(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user!.id;
      const claudeRequest: ClaudeRequest = req.body;
      
      const result = await claudeService.sendMessage(userId, claudeRequest);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(req: Request, res: Response, next: NextFunction) {
    try {
      const result = await claudeService.getModels();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
