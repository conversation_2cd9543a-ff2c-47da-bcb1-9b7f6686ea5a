# Claude Code Management System

一个共享Claude Max的镜像系统，提供CLI工具、Web界面和管理后台。

## 项目架构

```
claude-code-manage/
├── packages/
│   ├── cli/          # CLI工具 - 用户通过npm全局安装
│   ├── api/          # 后端API服务 - Express.js + TypeScript
│   ├── web/          # 用户Web前端 - React + TypeScript
│   ├── admin/        # 管理后台 - React + TypeScript
│   └── shared/       # 共享类型定义和工具函数
├── docker/           # Docker配置文件
├── docs/            # 项目文档
└── scripts/         # 部署和维护脚本
```

## 功能特性

### CLI工具
- 通过npm全局安装：`npm install -g https://code.newcli.com/install --registry=https://registry.npmmirror.com`
- 用户认证和注册
- Claude命令代理
- 自动token管理

### 用户Web界面
- 用户登录/注册
- 卡密兑换功能
- Token使用明细查看
- 账户信息管理

### 管理后台
- 用户管理
- 套餐卡密管理
- Claude Max维护
- 系统监控和统计

### 后端API
- 用户认证和授权
- Claude API代理
- 使用量统计和限制
- 卡密系统
- 数据持久化

## 快速开始

### 开发环境设置

1. 克隆项目
```bash
git clone https://github.com/your-username/claude-code-manage.git
cd claude-code-manage
```

2. 安装依赖
```bash
npm run install:all
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

4. 启动开发服务器
```bash
npm run dev
```

### 生产部署

1. 构建项目
```bash
npm run build
```

2. 使用Docker部署
```bash
docker-compose up -d
```

## 环境变量配置

```env
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/claude_code_manage

# JWT密钥
JWT_SECRET=your-jwt-secret-key

# Claude API配置
CLAUDE_API_KEY=your-claude-api-key
CLAUDE_API_BASE_URL=https://api.anthropic.com

# 服务端口
API_PORT=3001
WEB_PORT=3000
ADMIN_PORT=3002

# Redis配置（用于缓存和会话）
REDIS_URL=redis://localhost:6379

# 邮件服务配置（用于用户注册验证）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

## 技术栈

- **CLI**: Node.js + TypeScript + Commander.js
- **后端**: Express.js + TypeScript + Prisma + PostgreSQL
- **前端**: React + TypeScript + Vite + Tailwind CSS
- **管理后台**: React + TypeScript + Vite + Ant Design
- **数据库**: PostgreSQL + Redis
- **部署**: Docker + Docker Compose

## 开发指南

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier配置
- 提交前运行测试和代码检查

### 测试
```bash
npm test                # 运行所有测试
npm run test:api        # 运行API测试
npm run test:web        # 运行Web前端测试
npm run test:admin      # 运行管理后台测试
npm run test:cli        # 运行CLI测试
```

### 代码检查
```bash
npm run lint            # 检查代码规范
npm run lint:fix        # 自动修复代码规范问题
```

## 许可证

MIT License
