// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  username      String   @unique
  password      String
  role          Role     @default(USER)
  isActive      Boolean  @default(true)
  
  // 使用限制
  dailyLimit    Int      @default(1000)
  monthlyLimit  Int      @default(30000)
  dailyUsed     Int      @default(0)
  monthlyUsed   Int      @default(0)
  
  // 时间戳
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  lastLoginAt   DateTime?
  
  // 关联关系
  usageRecords  UsageRecord[]
  redeemedCards Card[]    @relation("CardRedeemer")
  sessions      Session[]
  
  @@map("users")
}

// 用户角色枚举
enum Role {
  USER
  ADMIN
}

// 会话表（用于JWT token管理）
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// 卡密表
model Card {
  id        String    @id @default(cuid())
  code      String    @unique
  type      CardType
  value     Int       // 增加的使用量
  isUsed    Boolean   @default(false)
  
  // 使用信息
  usedBy    String?
  usedAt    DateTime?
  
  // 时间戳
  createdAt DateTime  @default(now())
  expiresAt DateTime?
  
  // 关联关系
  redeemer  User?     @relation("CardRedeemer", fields: [usedBy], references: [id])
  
  @@map("cards")
}

// 卡密类型枚举
enum CardType {
  DAILY
  MONTHLY
  YEARLY
}

// Claude使用记录表
model UsageRecord {
  id           String   @id @default(cuid())
  userId       String
  model        String
  inputTokens  Int      @default(0)
  outputTokens Int      @default(0)
  totalTokens  Int      @default(0)
  cost         Float    @default(0)
  requestId    String   @unique
  
  // 请求详情
  prompt       String?  @db.Text
  response     String?  @db.Text
  
  // 时间戳
  createdAt    DateTime @default(now())
  
  // 关联关系
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("usage_records")
}

// 系统配置表
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String @db.Text
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("system_configs")
}

// 操作日志表
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  details   Json?
  ipAddress String?
  userAgent String?
  
  createdAt DateTime @default(now())
  
  @@map("audit_logs")
}
